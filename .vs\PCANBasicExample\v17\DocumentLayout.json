{"Version": 1, "WorkspaceRootPath": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|d:\\downloads\\pcan-basic\\samples\\samples\\gui\\c#\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\pcanbasic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|solutionrelative:pcanbasic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|solutionrelative:app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBCC19A-D35C-493D-A04C-DE1AA7E31F0D}|PCANBasicExample.csproj|solutionrelative:form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "app.config", "DocumentMoniker": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\app.config", "RelativeDocumentMoniker": "app.config", "ToolTip": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\app.config", "RelativeToolTip": "app.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-09-15T05:43:27.449Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Form1.cs", "DocumentMoniker": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\Form1.cs", "RelativeDocumentMoniker": "Form1.cs", "ToolTip": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\Form1.cs", "RelativeToolTip": "Form1.cs", "ViewState": "AgIAAD0CAAAAAAAAAIAxwDICAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-15T05:42:51.956Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "PCANBasic.cs", "DocumentMoniker": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\PCANBasic.cs", "RelativeDocumentMoniker": "PCANBasic.cs", "ToolTip": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\PCANBasic.cs", "RelativeToolTip": "PCANBasic.cs", "ViewState": "AgIAAPMBAAAAAAAAAAAmwDACAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-15T05:42:08.387Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "D:\\Downloads\\PCAN-Basic\\Samples\\Samples\\GUI\\C#\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-15T05:41:57.41Z", "EditorCaption": ""}]}]}]}