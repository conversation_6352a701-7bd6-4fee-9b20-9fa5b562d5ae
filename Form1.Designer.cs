﻿namespace ICDIBasic
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
			this.components = new System.ComponentModel.Container();
			this.groupBox1 = new System.Windows.Forms.GroupBox();
			this.chbCanFD = new System.Windows.Forms.CheckBox();
			this.cbbHwType = new System.Windows.Forms.ComboBox();
			this.cbbInterrupt = new System.Windows.Forms.ComboBox();
			this.laInterrupt = new System.Windows.Forms.Label();
			this.cbbIO = new System.Windows.Forms.ComboBox();
			this.laIOPort = new System.Windows.Forms.Label();
			this.laHwType = new System.Windows.Forms.Label();
			this.cbbBaudrates = new System.Windows.Forms.ComboBox();
			this.laBaudrate = new System.Windows.Forms.Label();
			this.txtBitrate = new System.Windows.Forms.TextBox();
			this.laBitrate = new System.Windows.Forms.Label();
			this.btnHwRefresh = new System.Windows.Forms.Button();
			this.cbbChannel = new System.Windows.Forms.ComboBox();
			this.label1 = new System.Windows.Forms.Label();
			this.btnInit = new System.Windows.Forms.Button();
			this.btnRelease = new System.Windows.Forms.Button();
			this.groupBox3 = new System.Windows.Forms.GroupBox();
			this.btnFilterQuery = new System.Windows.Forms.Button();
			this.chbFilterExt = new System.Windows.Forms.CheckBox();
			this.nudIdTo = new System.Windows.Forms.NumericUpDown();
			this.nudIdFrom = new System.Windows.Forms.NumericUpDown();
			this.label8 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.rdbFilterOpen = new System.Windows.Forms.RadioButton();
			this.rdbFilterCustom = new System.Windows.Forms.RadioButton();
			this.rdbFilterClose = new System.Windows.Forms.RadioButton();
			this.btnFilterApply = new System.Windows.Forms.Button();
			this.btnParameterSet = new System.Windows.Forms.Button();
			this.groupBox2 = new System.Windows.Forms.GroupBox();
			this.nudDelay = new System.Windows.Forms.NumericUpDown();
			this.btnParameterGet = new System.Windows.Forms.Button();
			this.label10 = new System.Windows.Forms.Label();
			this.nudDeviceId = new System.Windows.Forms.NumericUpDown();
			this.laDeviceOrDelay = new System.Windows.Forms.Label();
			this.cbbParameter = new System.Windows.Forms.ComboBox();
			this.label6 = new System.Windows.Forms.Label();
			this.rdbParamActive = new System.Windows.Forms.RadioButton();
			this.rdbParamInactive = new System.Windows.Forms.RadioButton();
			this.groupBox4 = new System.Windows.Forms.GroupBox();
			this.btnReset = new System.Windows.Forms.Button();
			this.btnStatus = new System.Windows.Forms.Button();
			this.btnGetVersions = new System.Windows.Forms.Button();
			this.lbxInfo = new System.Windows.Forms.ListBox();
			this.btnInfoClear = new System.Windows.Forms.Button();
			this.groupBox5 = new System.Windows.Forms.GroupBox();
			this.chbShowPeriod = new System.Windows.Forms.CheckBox();
			this.rdbManual = new System.Windows.Forms.RadioButton();
			this.rdbEvent = new System.Windows.Forms.RadioButton();
			this.lstMessages = new System.Windows.Forms.ListView();
			this.clhType = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
			this.clhID = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
			this.clhLength = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
			this.clhCount = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
			this.clhRcvTime = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
			this.clhData = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
			this.btnMsgClear = new System.Windows.Forms.Button();
			this.rdbTimer = new System.Windows.Forms.RadioButton();
			this.btnRead = new System.Windows.Forms.Button();
			this.groupBox6 = new System.Windows.Forms.GroupBox();
			this.label5 = new System.Windows.Forms.Label();
			this.laLength = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.txtData60 = new System.Windows.Forms.TextBox();
			this.txtData62 = new System.Windows.Forms.TextBox();
			this.txtData47 = new System.Windows.Forms.TextBox();
			this.txtData58 = new System.Windows.Forms.TextBox();
			this.txtData57 = new System.Windows.Forms.TextBox();
			this.txtData56 = new System.Windows.Forms.TextBox();
			this.txtData55 = new System.Windows.Forms.TextBox();
			this.txtData54 = new System.Windows.Forms.TextBox();
			this.txtData61 = new System.Windows.Forms.TextBox();
			this.txtData63 = new System.Windows.Forms.TextBox();
			this.txtData48 = new System.Windows.Forms.TextBox();
			this.txtData53 = new System.Windows.Forms.TextBox();
			this.txtData52 = new System.Windows.Forms.TextBox();
			this.txtData51 = new System.Windows.Forms.TextBox();
			this.txtData50 = new System.Windows.Forms.TextBox();
			this.txtData49 = new System.Windows.Forms.TextBox();
			this.txtData59 = new System.Windows.Forms.TextBox();
			this.txtData46 = new System.Windows.Forms.TextBox();
			this.txtData45 = new System.Windows.Forms.TextBox();
			this.txtData44 = new System.Windows.Forms.TextBox();
			this.txtData43 = new System.Windows.Forms.TextBox();
			this.txtData42 = new System.Windows.Forms.TextBox();
			this.txtData41 = new System.Windows.Forms.TextBox();
			this.txtData40 = new System.Windows.Forms.TextBox();
			this.txtData39 = new System.Windows.Forms.TextBox();
			this.txtData38 = new System.Windows.Forms.TextBox();
			this.txtData37 = new System.Windows.Forms.TextBox();
			this.txtData36 = new System.Windows.Forms.TextBox();
			this.txtData35 = new System.Windows.Forms.TextBox();
			this.txtData34 = new System.Windows.Forms.TextBox();
			this.txtData33 = new System.Windows.Forms.TextBox();
			this.txtData32 = new System.Windows.Forms.TextBox();
			this.txtData31 = new System.Windows.Forms.TextBox();
			this.txtData30 = new System.Windows.Forms.TextBox();
			this.txtData29 = new System.Windows.Forms.TextBox();
			this.txtData28 = new System.Windows.Forms.TextBox();
			this.txtData27 = new System.Windows.Forms.TextBox();
			this.txtData26 = new System.Windows.Forms.TextBox();
			this.txtData25 = new System.Windows.Forms.TextBox();
			this.txtData24 = new System.Windows.Forms.TextBox();
			this.txtData23 = new System.Windows.Forms.TextBox();
			this.txtData22 = new System.Windows.Forms.TextBox();
			this.txtData21 = new System.Windows.Forms.TextBox();
			this.txtData20 = new System.Windows.Forms.TextBox();
			this.txtData19 = new System.Windows.Forms.TextBox();
			this.txtData18 = new System.Windows.Forms.TextBox();
			this.txtData17 = new System.Windows.Forms.TextBox();
			this.txtData16 = new System.Windows.Forms.TextBox();
			this.txtData15 = new System.Windows.Forms.TextBox();
			this.txtData14 = new System.Windows.Forms.TextBox();
			this.txtData13 = new System.Windows.Forms.TextBox();
			this.txtData12 = new System.Windows.Forms.TextBox();
			this.txtData11 = new System.Windows.Forms.TextBox();
			this.txtData10 = new System.Windows.Forms.TextBox();
			this.txtData9 = new System.Windows.Forms.TextBox();
			this.txtData8 = new System.Windows.Forms.TextBox();
			this.chbBRS = new System.Windows.Forms.CheckBox();
			this.chbFD = new System.Windows.Forms.CheckBox();
			this.chbRemote = new System.Windows.Forms.CheckBox();
			this.chbExtended = new System.Windows.Forms.CheckBox();
			this.btnWrite = new System.Windows.Forms.Button();
			this.label12 = new System.Windows.Forms.Label();
			this.label13 = new System.Windows.Forms.Label();
			this.txtID = new System.Windows.Forms.TextBox();
			this.txtData7 = new System.Windows.Forms.TextBox();
			this.txtData6 = new System.Windows.Forms.TextBox();
			this.txtData5 = new System.Windows.Forms.TextBox();
			this.txtData4 = new System.Windows.Forms.TextBox();
			this.txtData3 = new System.Windows.Forms.TextBox();
			this.txtData2 = new System.Windows.Forms.TextBox();
			this.txtData1 = new System.Windows.Forms.TextBox();
			this.txtData0 = new System.Windows.Forms.TextBox();
			this.nudLength = new System.Windows.Forms.NumericUpDown();
			this.tmrRead = new System.Windows.Forms.Timer(this.components);
			this.tmrDisplay = new System.Windows.Forms.Timer(this.components);
			this.groupBox1.SuspendLayout();
			this.groupBox3.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nudIdTo)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nudIdFrom)).BeginInit();
			this.groupBox2.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nudDelay)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nudDeviceId)).BeginInit();
			this.groupBox4.SuspendLayout();
			this.groupBox5.SuspendLayout();
			this.groupBox6.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nudLength)).BeginInit();
			this.SuspendLayout();
			// 
			// groupBox1
			// 
			this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.groupBox1.Controls.Add(this.chbCanFD);
			this.groupBox1.Controls.Add(this.cbbHwType);
			this.groupBox1.Controls.Add(this.cbbInterrupt);
			this.groupBox1.Controls.Add(this.laInterrupt);
			this.groupBox1.Controls.Add(this.cbbIO);
			this.groupBox1.Controls.Add(this.laIOPort);
			this.groupBox1.Controls.Add(this.laHwType);
			this.groupBox1.Controls.Add(this.cbbBaudrates);
			this.groupBox1.Controls.Add(this.laBaudrate);
			this.groupBox1.Controls.Add(this.txtBitrate);
			this.groupBox1.Controls.Add(this.laBitrate);
			this.groupBox1.Controls.Add(this.btnHwRefresh);
			this.groupBox1.Controls.Add(this.cbbChannel);
			this.groupBox1.Controls.Add(this.label1);
			this.groupBox1.Controls.Add(this.btnInit);
			this.groupBox1.Controls.Add(this.btnRelease);
			this.groupBox1.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.groupBox1.Location = new System.Drawing.Point(8, 8);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new System.Drawing.Size(714, 65);
			this.groupBox1.TabIndex = 42;
			this.groupBox1.TabStop = false;
			this.groupBox1.Text = " Connection ";
			// 
			// chbCanFD
			// 
			this.chbCanFD.AutoSize = true;
			this.chbCanFD.Location = new System.Drawing.Point(574, 34);
			this.chbCanFD.Name = "chbCanFD";
			this.chbCanFD.Size = new System.Drawing.Size(65, 17);
			this.chbCanFD.TabIndex = 59;
			this.chbCanFD.Text = "CAN-FD";
			this.chbCanFD.UseVisualStyleBackColor = true;
			this.chbCanFD.CheckedChanged += new System.EventHandler(this.chbCanFD_CheckedChanged);
			// 
			// cbbHwType
			// 
			this.cbbHwType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cbbHwType.Items.AddRange(new object[] {
            "ISA-82C200",
            "ISA-SJA1000",
            "ISA-PHYTEC",
            "DNG-82C200",
            "DNG-82C200 EPP",
            "DNG-SJA1000",
            "DNG-SJA1000 EPP"});
			this.cbbHwType.Location = new System.Drawing.Point(326, 31);
			this.cbbHwType.Name = "cbbHwType";
			this.cbbHwType.Size = new System.Drawing.Size(120, 21);
			this.cbbHwType.TabIndex = 50;
			this.cbbHwType.SelectedIndexChanged += new System.EventHandler(this.cbbHwType_SelectedIndexChanged);
			// 
			// cbbInterrupt
			// 
			this.cbbInterrupt.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cbbInterrupt.Items.AddRange(new object[] {
            "3",
            "4",
            "5",
            "7",
            "9",
            "10",
            "11",
            "12",
            "15"});
			this.cbbInterrupt.Location = new System.Drawing.Point(513, 31);
			this.cbbInterrupt.Name = "cbbInterrupt";
			this.cbbInterrupt.Size = new System.Drawing.Size(55, 21);
			this.cbbInterrupt.TabIndex = 52;
			// 
			// laInterrupt
			// 
			this.laInterrupt.Location = new System.Drawing.Point(515, 15);
			this.laInterrupt.Name = "laInterrupt";
			this.laInterrupt.Size = new System.Drawing.Size(53, 23);
			this.laInterrupt.TabIndex = 56;
			this.laInterrupt.Text = "Interrupt:";
			// 
			// cbbIO
			// 
			this.cbbIO.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cbbIO.Items.AddRange(new object[] {
            "0100",
            "0120",
            "0140",
            "0200",
            "0220",
            "0240",
            "0260",
            "0278",
            "0280",
            "02A0",
            "02C0",
            "02E0",
            "02E8",
            "02F8",
            "0300",
            "0320",
            "0340",
            "0360",
            "0378",
            "0380",
            "03BC",
            "03E0",
            "03E8",
            "03F8"});
			this.cbbIO.Location = new System.Drawing.Point(452, 31);
			this.cbbIO.Name = "cbbIO";
			this.cbbIO.Size = new System.Drawing.Size(55, 21);
			this.cbbIO.TabIndex = 51;
			// 
			// laIOPort
			// 
			this.laIOPort.Location = new System.Drawing.Point(452, 15);
			this.laIOPort.Name = "laIOPort";
			this.laIOPort.Size = new System.Drawing.Size(55, 23);
			this.laIOPort.TabIndex = 55;
			this.laIOPort.Text = "I/O Port:";
			// 
			// laHwType
			// 
			this.laHwType.Location = new System.Drawing.Point(327, 15);
			this.laHwType.Name = "laHwType";
			this.laHwType.Size = new System.Drawing.Size(90, 23);
			this.laHwType.TabIndex = 54;
			this.laHwType.Text = "Hardware Type:";
			// 
			// cbbBaudrates
			// 
			this.cbbBaudrates.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cbbBaudrates.Items.AddRange(new object[] {
            "1 MBit/sec",
            "800 kBit/s",
            "500 kBit/sec",
            "250 kBit/sec",
            "125 kBit/sec",
            "100 kBit/sec",
            "95,238 kBit/s",
            "83,333 kBit/s",
            "50 kBit/sec",
            "47,619 kBit/s",
            "33,333 kBit/s",
            "20 kBit/sec",
            "10 kBit/sec",
            "5 kBit/sec"});
			this.cbbBaudrates.Location = new System.Drawing.Point(204, 31);
			this.cbbBaudrates.Name = "cbbBaudrates";
			this.cbbBaudrates.Size = new System.Drawing.Size(116, 21);
			this.cbbBaudrates.TabIndex = 49;
			this.cbbBaudrates.SelectedIndexChanged += new System.EventHandler(this.cbbBaudrates_SelectedIndexChanged);
			// 
			// laBaudrate
			// 
			this.laBaudrate.Location = new System.Drawing.Point(204, 15);
			this.laBaudrate.Name = "laBaudrate";
			this.laBaudrate.Size = new System.Drawing.Size(56, 23);
			this.laBaudrate.TabIndex = 53;
			this.laBaudrate.Text = "Baudrate:";
			// 
			// txtBitrate
			// 
			this.txtBitrate.Location = new System.Drawing.Point(204, 25);
			this.txtBitrate.Multiline = true;
			this.txtBitrate.Name = "txtBitrate";
			this.txtBitrate.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
			this.txtBitrate.Size = new System.Drawing.Size(364, 34);
			this.txtBitrate.TabIndex = 48;
			this.txtBitrate.Visible = false;
			// 
			// laBitrate
			// 
			this.laBitrate.AutoSize = true;
			this.laBitrate.Location = new System.Drawing.Point(201, 8);
			this.laBitrate.Name = "laBitrate";
			this.laBitrate.Size = new System.Drawing.Size(43, 13);
			this.laBitrate.TabIndex = 46;
			this.laBitrate.Text = "Bit rate:";
			this.laBitrate.Visible = false;
			// 
			// btnHwRefresh
			// 
			this.btnHwRefresh.Cursor = System.Windows.Forms.Cursors.Default;
			this.btnHwRefresh.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnHwRefresh.Location = new System.Drawing.Point(141, 30);
			this.btnHwRefresh.Name = "btnHwRefresh";
			this.btnHwRefresh.Size = new System.Drawing.Size(57, 23);
			this.btnHwRefresh.TabIndex = 45;
			this.btnHwRefresh.Text = "Refresh";
			this.btnHwRefresh.Click += new System.EventHandler(this.btnHwRefresh_Click);
			// 
			// cbbChannel
			// 
			this.cbbChannel.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cbbChannel.Font = new System.Drawing.Font("Consolas", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.cbbChannel.Items.AddRange(new object[] {
            "None",
            "DNG-Channel 1",
            "ISA-Channel 1",
            "ISA-Channel 2",
            "ISA-Channel 3",
            "ISA-Channel 4",
            "ISA-Channel 5",
            "ISA-Channel 6",
            "ISA-Channel 7",
            "ISA-Channel 8",
            "PCC-Channel 1",
            "PCC-Channel 2",
            "PCI-Channel 1",
            "PCI-Channel 2",
            "PCI-Channel 3",
            "PCI-Channel 4",
            "PCI-Channel 5",
            "PCI-Channel 6",
            "PCI-Channel 7",
            "PCI-Channel 8",
            "USB-Channel 1",
            "USB-Channel 2",
            "USB-Channel 3",
            "USB-Channel 4",
            "USB-Channel 5",
            "USB-Channel 6",
            "USB-Channel 7",
            "USB-Channel 8"});
			this.cbbChannel.Location = new System.Drawing.Point(8, 31);
			this.cbbChannel.Name = "cbbChannel";
			this.cbbChannel.Size = new System.Drawing.Size(127, 21);
			this.cbbChannel.TabIndex = 32;
			this.cbbChannel.SelectedIndexChanged += new System.EventHandler(this.cbbChannel_SelectedIndexChanged);
			// 
			// label1
			// 
			this.label1.Location = new System.Drawing.Point(7, 16);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(56, 23);
			this.label1.TabIndex = 40;
			this.label1.Text = "Hardware:";
			// 
			// btnInit
			// 
			this.btnInit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnInit.Cursor = System.Windows.Forms.Cursors.Default;
			this.btnInit.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnInit.Location = new System.Drawing.Point(643, 11);
			this.btnInit.Name = "btnInit";
			this.btnInit.Size = new System.Drawing.Size(65, 23);
			this.btnInit.TabIndex = 34;
			this.btnInit.Text = "Initialize";
			this.btnInit.Click += new System.EventHandler(this.btnInit_Click);
			// 
			// btnRelease
			// 
			this.btnRelease.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnRelease.Cursor = System.Windows.Forms.Cursors.Default;
			this.btnRelease.Enabled = false;
			this.btnRelease.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnRelease.Location = new System.Drawing.Point(643, 36);
			this.btnRelease.Name = "btnRelease";
			this.btnRelease.Size = new System.Drawing.Size(65, 23);
			this.btnRelease.TabIndex = 35;
			this.btnRelease.Text = "Release";
			this.btnRelease.Click += new System.EventHandler(this.btnRelease_Click);
			// 
			// groupBox3
			// 
			this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.groupBox3.Controls.Add(this.btnFilterQuery);
			this.groupBox3.Controls.Add(this.chbFilterExt);
			this.groupBox3.Controls.Add(this.nudIdTo);
			this.groupBox3.Controls.Add(this.nudIdFrom);
			this.groupBox3.Controls.Add(this.label8);
			this.groupBox3.Controls.Add(this.label7);
			this.groupBox3.Controls.Add(this.rdbFilterOpen);
			this.groupBox3.Controls.Add(this.rdbFilterCustom);
			this.groupBox3.Controls.Add(this.rdbFilterClose);
			this.groupBox3.Controls.Add(this.btnFilterApply);
			this.groupBox3.Location = new System.Drawing.Point(8, 79);
			this.groupBox3.Name = "groupBox3";
			this.groupBox3.Size = new System.Drawing.Size(714, 58);
			this.groupBox3.TabIndex = 44;
			this.groupBox3.TabStop = false;
			this.groupBox3.Text = " Message Filtering ";
			// 
			// btnFilterQuery
			// 
			this.btnFilterQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnFilterQuery.Enabled = false;
			this.btnFilterQuery.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnFilterQuery.Location = new System.Drawing.Point(643, 26);
			this.btnFilterQuery.Name = "btnFilterQuery";
			this.btnFilterQuery.Size = new System.Drawing.Size(65, 23);
			this.btnFilterQuery.TabIndex = 55;
			this.btnFilterQuery.Text = "Query";
			this.btnFilterQuery.UseVisualStyleBackColor = true;
			this.btnFilterQuery.Click += new System.EventHandler(this.btnFilterQuery_Click);
			// 
			// chbFilterExt
			// 
			this.chbFilterExt.AutoSize = true;
			this.chbFilterExt.Location = new System.Drawing.Point(10, 33);
			this.chbFilterExt.Name = "chbFilterExt";
			this.chbFilterExt.Size = new System.Drawing.Size(103, 17);
			this.chbFilterExt.TabIndex = 44;
			this.chbFilterExt.Text = "Extended Frame";
			this.chbFilterExt.UseVisualStyleBackColor = true;
			this.chbFilterExt.CheckedChanged += new System.EventHandler(this.chbFilterExt_CheckedChanged);
			// 
			// nudIdTo
			// 
			this.nudIdTo.Hexadecimal = true;
			this.nudIdTo.Location = new System.Drawing.Point(438, 29);
			this.nudIdTo.Maximum = new decimal(new int[] {
            2047,
            0,
            0,
            0});
			this.nudIdTo.Name = "nudIdTo";
			this.nudIdTo.Size = new System.Drawing.Size(69, 20);
			this.nudIdTo.TabIndex = 6;
			this.nudIdTo.Value = new decimal(new int[] {
            2047,
            0,
            0,
            0});
			// 
			// nudIdFrom
			// 
			this.nudIdFrom.Hexadecimal = true;
			this.nudIdFrom.Location = new System.Drawing.Point(363, 29);
			this.nudIdFrom.Maximum = new decimal(new int[] {
            2047,
            0,
            0,
            0});
			this.nudIdFrom.Name = "nudIdFrom";
			this.nudIdFrom.Size = new System.Drawing.Size(69, 20);
			this.nudIdFrom.TabIndex = 5;
			// 
			// label8
			// 
			this.label8.Location = new System.Drawing.Point(436, 12);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(61, 23);
			this.label8.TabIndex = 43;
			this.label8.Text = "To (Hex):";
			// 
			// label7
			// 
			this.label7.Location = new System.Drawing.Point(361, 12);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(69, 23);
			this.label7.TabIndex = 42;
			this.label7.Text = "From (Hex):";
			// 
			// rdbFilterOpen
			// 
			this.rdbFilterOpen.Checked = true;
			this.rdbFilterOpen.Location = new System.Drawing.Point(120, 32);
			this.rdbFilterOpen.Name = "rdbFilterOpen";
			this.rdbFilterOpen.Size = new System.Drawing.Size(53, 17);
			this.rdbFilterOpen.TabIndex = 2;
			this.rdbFilterOpen.TabStop = true;
			this.rdbFilterOpen.Text = "Open";
			this.rdbFilterOpen.UseVisualStyleBackColor = true;
			// 
			// rdbFilterCustom
			// 
			this.rdbFilterCustom.Location = new System.Drawing.Point(238, 32);
			this.rdbFilterCustom.Name = "rdbFilterCustom";
			this.rdbFilterCustom.Size = new System.Drawing.Size(104, 17);
			this.rdbFilterCustom.TabIndex = 1;
			this.rdbFilterCustom.Text = "Custom (expand)";
			this.rdbFilterCustom.UseVisualStyleBackColor = true;
			// 
			// rdbFilterClose
			// 
			this.rdbFilterClose.Location = new System.Drawing.Point(177, 32);
			this.rdbFilterClose.Name = "rdbFilterClose";
			this.rdbFilterClose.Size = new System.Drawing.Size(58, 17);
			this.rdbFilterClose.TabIndex = 0;
			this.rdbFilterClose.Text = "Close";
			this.rdbFilterClose.UseVisualStyleBackColor = true;
			// 
			// btnFilterApply
			// 
			this.btnFilterApply.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnFilterApply.Enabled = false;
			this.btnFilterApply.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnFilterApply.Location = new System.Drawing.Point(572, 26);
			this.btnFilterApply.Name = "btnFilterApply";
			this.btnFilterApply.Size = new System.Drawing.Size(65, 23);
			this.btnFilterApply.TabIndex = 44;
			this.btnFilterApply.Text = "Apply";
			this.btnFilterApply.UseVisualStyleBackColor = true;
			this.btnFilterApply.Click += new System.EventHandler(this.btnFilterApply_Click);
			// 
			// btnParameterSet
			// 
			this.btnParameterSet.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnParameterSet.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnParameterSet.Location = new System.Drawing.Point(572, 26);
			this.btnParameterSet.Name = "btnParameterSet";
			this.btnParameterSet.Size = new System.Drawing.Size(65, 23);
			this.btnParameterSet.TabIndex = 46;
			this.btnParameterSet.Text = "Set";
			this.btnParameterSet.UseVisualStyleBackColor = true;
			this.btnParameterSet.Click += new System.EventHandler(this.btnParameterSet_Click);
			// 
			// groupBox2
			// 
			this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.groupBox2.Controls.Add(this.nudDelay);
			this.groupBox2.Controls.Add(this.btnParameterGet);
			this.groupBox2.Controls.Add(this.label10);
			this.groupBox2.Controls.Add(this.nudDeviceId);
			this.groupBox2.Controls.Add(this.laDeviceOrDelay);
			this.groupBox2.Controls.Add(this.cbbParameter);
			this.groupBox2.Controls.Add(this.label6);
			this.groupBox2.Controls.Add(this.rdbParamActive);
			this.groupBox2.Controls.Add(this.rdbParamInactive);
			this.groupBox2.Controls.Add(this.btnParameterSet);
			this.groupBox2.Location = new System.Drawing.Point(8, 143);
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.Size = new System.Drawing.Size(714, 58);
			this.groupBox2.TabIndex = 45;
			this.groupBox2.TabStop = false;
			this.groupBox2.Text = " Configuration Parameters ";
			// 
			// nudDelay
			// 
			this.nudDelay.Location = new System.Drawing.Point(408, 29);
			this.nudDelay.Maximum = new decimal(new int[] {
            -1,
            0,
            0,
            0});
			this.nudDelay.Name = "nudDelay";
			this.nudDelay.Size = new System.Drawing.Size(99, 20);
			this.nudDelay.TabIndex = 55;
			// 
			// btnParameterGet
			// 
			this.btnParameterGet.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnParameterGet.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnParameterGet.Location = new System.Drawing.Point(643, 26);
			this.btnParameterGet.Name = "btnParameterGet";
			this.btnParameterGet.Size = new System.Drawing.Size(65, 23);
			this.btnParameterGet.TabIndex = 54;
			this.btnParameterGet.Text = "Get";
			this.btnParameterGet.UseVisualStyleBackColor = true;
			this.btnParameterGet.Click += new System.EventHandler(this.btnParameterGet_Click);
			// 
			// label10
			// 
			this.label10.Location = new System.Drawing.Point(241, 11);
			this.label10.Name = "label10";
			this.label10.Size = new System.Drawing.Size(59, 23);
			this.label10.TabIndex = 46;
			this.label10.Text = "Activation:";
			// 
			// nudDeviceId
			// 
			this.nudDeviceId.Enabled = false;
			this.nudDeviceId.Hexadecimal = true;
			this.nudDeviceId.Location = new System.Drawing.Point(408, 29);
			this.nudDeviceId.Maximum = new decimal(new int[] {
            -1,
            0,
            0,
            0});
			this.nudDeviceId.Name = "nudDeviceId";
			this.nudDeviceId.Size = new System.Drawing.Size(99, 20);
			this.nudDeviceId.TabIndex = 6;
			// 
			// laDeviceOrDelay
			// 
			this.laDeviceOrDelay.Location = new System.Drawing.Point(405, 12);
			this.laDeviceOrDelay.Name = "laDeviceOrDelay";
			this.laDeviceOrDelay.Size = new System.Drawing.Size(102, 23);
			this.laDeviceOrDelay.TabIndex = 45;
			this.laDeviceOrDelay.Text = "Device ID (Hex):";
			// 
			// cbbParameter
			// 
			this.cbbParameter.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cbbParameter.FormattingEnabled = true;
			this.cbbParameter.Items.AddRange(new object[] {
            "Device ID",
            "5V Power",
            "Auto-reset on BUS-OFF",
            "CAN Listen-Only",
            "Debug\'s Log",
            "Receive Status",
            "CAN Controller Number",
            "Trace File",
            "Channel Identification (USB)",
            "Channel Capabilities",
            "Bit rate Adaptation",
            "Get Bit rate Information",
            "Get Bit rate FD Information",
            "Get CAN Nominal Speed Bit/s",
            "Get CAN Data Speed Bit/s",
            "Get IP Address",
            "Get LAN Service Status",
            "Reception of Status Frames",
            "Reception of RTR Frames",
            "Reception of Error Frames",
            "Interframe Transmit Delay",
            "Reception of Echo Frames",
            "Hard Reset Status",
            "Communication Direction",
            "Global Unique Identifier (GUID)"});
			this.cbbParameter.Location = new System.Drawing.Point(10, 31);
			this.cbbParameter.Name = "cbbParameter";
			this.cbbParameter.Size = new System.Drawing.Size(217, 21);
			this.cbbParameter.TabIndex = 44;
			this.cbbParameter.SelectedIndexChanged += new System.EventHandler(this.cbbParameter_SelectedIndexChanged);
			// 
			// label6
			// 
			this.label6.Location = new System.Drawing.Point(7, 14);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(64, 23);
			this.label6.TabIndex = 43;
			this.label6.Text = "Parameter:";
			// 
			// rdbParamActive
			// 
			this.rdbParamActive.Checked = true;
			this.rdbParamActive.Location = new System.Drawing.Point(238, 32);
			this.rdbParamActive.Name = "rdbParamActive";
			this.rdbParamActive.Size = new System.Drawing.Size(56, 17);
			this.rdbParamActive.TabIndex = 2;
			this.rdbParamActive.TabStop = true;
			this.rdbParamActive.Text = "Active";
			this.rdbParamActive.UseVisualStyleBackColor = true;
			// 
			// rdbParamInactive
			// 
			this.rdbParamInactive.Location = new System.Drawing.Point(300, 32);
			this.rdbParamInactive.Name = "rdbParamInactive";
			this.rdbParamInactive.Size = new System.Drawing.Size(67, 17);
			this.rdbParamInactive.TabIndex = 0;
			this.rdbParamInactive.Text = "Inactive";
			this.rdbParamInactive.UseVisualStyleBackColor = true;
			// 
			// groupBox4
			// 
			this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.groupBox4.Controls.Add(this.btnReset);
			this.groupBox4.Controls.Add(this.btnStatus);
			this.groupBox4.Controls.Add(this.btnGetVersions);
			this.groupBox4.Controls.Add(this.lbxInfo);
			this.groupBox4.Controls.Add(this.btnInfoClear);
			this.groupBox4.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.groupBox4.Location = new System.Drawing.Point(8, 506);
			this.groupBox4.Name = "groupBox4";
			this.groupBox4.Size = new System.Drawing.Size(714, 87);
			this.groupBox4.TabIndex = 47;
			this.groupBox4.TabStop = false;
			this.groupBox4.Text = "Information";
			// 
			// btnReset
			// 
			this.btnReset.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnReset.Enabled = false;
			this.btnReset.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnReset.Location = new System.Drawing.Point(643, 48);
			this.btnReset.Name = "btnReset";
			this.btnReset.Size = new System.Drawing.Size(65, 23);
			this.btnReset.TabIndex = 58;
			this.btnReset.Text = "Reset";
			this.btnReset.UseVisualStyleBackColor = true;
			this.btnReset.Click += new System.EventHandler(this.btnReset_Click);
			// 
			// btnStatus
			// 
			this.btnStatus.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnStatus.Enabled = false;
			this.btnStatus.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnStatus.Location = new System.Drawing.Point(572, 48);
			this.btnStatus.Name = "btnStatus";
			this.btnStatus.Size = new System.Drawing.Size(65, 23);
			this.btnStatus.TabIndex = 57;
			this.btnStatus.Text = "Status";
			this.btnStatus.UseVisualStyleBackColor = true;
			this.btnStatus.Click += new System.EventHandler(this.btnStatus_Click);
			// 
			// btnGetVersions
			// 
			this.btnGetVersions.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnGetVersions.Enabled = false;
			this.btnGetVersions.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnGetVersions.Location = new System.Drawing.Point(572, 19);
			this.btnGetVersions.Name = "btnGetVersions";
			this.btnGetVersions.Size = new System.Drawing.Size(65, 23);
			this.btnGetVersions.TabIndex = 53;
			this.btnGetVersions.Text = "Versions";
			this.btnGetVersions.UseVisualStyleBackColor = true;
			this.btnGetVersions.Click += new System.EventHandler(this.btnGetVersions_Click);
			// 
			// lbxInfo
			// 
			this.lbxInfo.FormattingEnabled = true;
			this.lbxInfo.Items.AddRange(new object[] {
            "Select a Hardware and a configuration for it. Then click \"Initialize\" button",
            "When activated, the Debug-Log file will be found in the same directory as this ap" +
                "plication",
            "When activated, the PCAN-Trace file will be found in the same directory as this a" +
                "pplication"});
			this.lbxInfo.Location = new System.Drawing.Point(10, 19);
			this.lbxInfo.Name = "lbxInfo";
			this.lbxInfo.ScrollAlwaysVisible = true;
			this.lbxInfo.Size = new System.Drawing.Size(558, 56);
			this.lbxInfo.TabIndex = 56;
			this.lbxInfo.DoubleClick += new System.EventHandler(this.lbxInfo_DoubleClick);
			// 
			// btnInfoClear
			// 
			this.btnInfoClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnInfoClear.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnInfoClear.Location = new System.Drawing.Point(643, 19);
			this.btnInfoClear.Name = "btnInfoClear";
			this.btnInfoClear.Size = new System.Drawing.Size(65, 23);
			this.btnInfoClear.TabIndex = 52;
			this.btnInfoClear.Text = "Clear";
			this.btnInfoClear.UseVisualStyleBackColor = true;
			this.btnInfoClear.Click += new System.EventHandler(this.btnInfoClear_Click);
			// 
			// groupBox5
			// 
			this.groupBox5.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.groupBox5.Controls.Add(this.chbShowPeriod);
			this.groupBox5.Controls.Add(this.rdbManual);
			this.groupBox5.Controls.Add(this.rdbEvent);
			this.groupBox5.Controls.Add(this.lstMessages);
			this.groupBox5.Controls.Add(this.btnMsgClear);
			this.groupBox5.Controls.Add(this.rdbTimer);
			this.groupBox5.Controls.Add(this.btnRead);
			this.groupBox5.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.groupBox5.Location = new System.Drawing.Point(8, 207);
			this.groupBox5.Name = "groupBox5";
			this.groupBox5.Size = new System.Drawing.Size(714, 140);
			this.groupBox5.TabIndex = 48;
			this.groupBox5.TabStop = false;
			this.groupBox5.Text = " Messages Reading ";
			// 
			// chbShowPeriod
			// 
			this.chbShowPeriod.AutoSize = true;
			this.chbShowPeriod.Checked = true;
			this.chbShowPeriod.CheckState = System.Windows.Forms.CheckState.Checked;
			this.chbShowPeriod.Location = new System.Drawing.Point(374, 15);
			this.chbShowPeriod.Name = "chbShowPeriod";
			this.chbShowPeriod.Size = new System.Drawing.Size(123, 17);
			this.chbShowPeriod.TabIndex = 75;
			this.chbShowPeriod.Text = "Timestamp as period";
			this.chbShowPeriod.UseVisualStyleBackColor = true;
			this.chbShowPeriod.CheckedChanged += new System.EventHandler(this.chbShowPeriod_CheckedChanged);
			// 
			// rdbManual
			// 
			this.rdbManual.AutoSize = true;
			this.rdbManual.Location = new System.Drawing.Point(276, 14);
			this.rdbManual.Name = "rdbManual";
			this.rdbManual.Size = new System.Drawing.Size(89, 17);
			this.rdbManual.TabIndex = 74;
			this.rdbManual.Text = "Manual Read";
			this.rdbManual.UseVisualStyleBackColor = true;
			this.rdbManual.CheckedChanged += new System.EventHandler(this.rdbTimer_CheckedChanged);
			// 
			// rdbEvent
			// 
			this.rdbEvent.AutoSize = true;
			this.rdbEvent.Location = new System.Drawing.Point(131, 14);
			this.rdbEvent.Name = "rdbEvent";
			this.rdbEvent.Size = new System.Drawing.Size(139, 17);
			this.rdbEvent.TabIndex = 73;
			this.rdbEvent.Text = "Reading using an Event";
			this.rdbEvent.UseVisualStyleBackColor = true;
			this.rdbEvent.CheckedChanged += new System.EventHandler(this.rdbTimer_CheckedChanged);
			// 
			// lstMessages
			// 
			this.lstMessages.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.clhType,
            this.clhID,
            this.clhLength,
            this.clhCount,
            this.clhRcvTime,
            this.clhData});
			this.lstMessages.FullRowSelect = true;
			this.lstMessages.HideSelection = false;
			this.lstMessages.Location = new System.Drawing.Point(8, 37);
			this.lstMessages.MultiSelect = false;
			this.lstMessages.Name = "lstMessages";
			this.lstMessages.Size = new System.Drawing.Size(560, 96);
			this.lstMessages.TabIndex = 28;
			this.lstMessages.UseCompatibleStateImageBehavior = false;
			this.lstMessages.View = System.Windows.Forms.View.Details;
			this.lstMessages.DoubleClick += new System.EventHandler(this.lstMessages_DoubleClick);
			// 
			// clhType
			// 
			this.clhType.Text = "Type";
			this.clhType.Width = 110;
			// 
			// clhID
			// 
			this.clhID.Text = "ID";
			this.clhID.Width = 90;
			// 
			// clhLength
			// 
			this.clhLength.Text = "Length";
			this.clhLength.Width = 50;
			// 
			// clhCount
			// 
			this.clhCount.Text = "Count";
			this.clhCount.Width = 49;
			// 
			// clhRcvTime
			// 
			this.clhRcvTime.Text = "Rcv Time";
			this.clhRcvTime.Width = 70;
			// 
			// clhData
			// 
			this.clhData.Text = "Data";
			this.clhData.Width = 170;
			// 
			// btnMsgClear
			// 
			this.btnMsgClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnMsgClear.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnMsgClear.Location = new System.Drawing.Point(643, 37);
			this.btnMsgClear.Name = "btnMsgClear";
			this.btnMsgClear.Size = new System.Drawing.Size(65, 23);
			this.btnMsgClear.TabIndex = 50;
			this.btnMsgClear.Text = "Clear";
			this.btnMsgClear.UseVisualStyleBackColor = true;
			this.btnMsgClear.Click += new System.EventHandler(this.btnMsgClear_Click);
			// 
			// rdbTimer
			// 
			this.rdbTimer.AutoSize = true;
			this.rdbTimer.Checked = true;
			this.rdbTimer.Location = new System.Drawing.Point(8, 14);
			this.rdbTimer.Name = "rdbTimer";
			this.rdbTimer.Size = new System.Drawing.Size(117, 17);
			this.rdbTimer.TabIndex = 72;
			this.rdbTimer.TabStop = true;
			this.rdbTimer.Text = "Read using a Timer";
			this.rdbTimer.UseVisualStyleBackColor = true;
			this.rdbTimer.CheckedChanged += new System.EventHandler(this.rdbTimer_CheckedChanged);
			// 
			// btnRead
			// 
			this.btnRead.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnRead.Enabled = false;
			this.btnRead.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnRead.Location = new System.Drawing.Point(572, 37);
			this.btnRead.Name = "btnRead";
			this.btnRead.Size = new System.Drawing.Size(65, 23);
			this.btnRead.TabIndex = 49;
			this.btnRead.Text = "Read";
			this.btnRead.UseVisualStyleBackColor = true;
			this.btnRead.Click += new System.EventHandler(this.btnRead_Click);
			// 
			// groupBox6
			// 
			this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.groupBox6.Controls.Add(this.label5);
			this.groupBox6.Controls.Add(this.laLength);
			this.groupBox6.Controls.Add(this.label3);
			this.groupBox6.Controls.Add(this.txtData60);
			this.groupBox6.Controls.Add(this.txtData62);
			this.groupBox6.Controls.Add(this.txtData47);
			this.groupBox6.Controls.Add(this.txtData58);
			this.groupBox6.Controls.Add(this.txtData57);
			this.groupBox6.Controls.Add(this.txtData56);
			this.groupBox6.Controls.Add(this.txtData55);
			this.groupBox6.Controls.Add(this.txtData54);
			this.groupBox6.Controls.Add(this.txtData61);
			this.groupBox6.Controls.Add(this.txtData63);
			this.groupBox6.Controls.Add(this.txtData48);
			this.groupBox6.Controls.Add(this.txtData53);
			this.groupBox6.Controls.Add(this.txtData52);
			this.groupBox6.Controls.Add(this.txtData51);
			this.groupBox6.Controls.Add(this.txtData50);
			this.groupBox6.Controls.Add(this.txtData49);
			this.groupBox6.Controls.Add(this.txtData59);
			this.groupBox6.Controls.Add(this.txtData46);
			this.groupBox6.Controls.Add(this.txtData45);
			this.groupBox6.Controls.Add(this.txtData44);
			this.groupBox6.Controls.Add(this.txtData43);
			this.groupBox6.Controls.Add(this.txtData42);
			this.groupBox6.Controls.Add(this.txtData41);
			this.groupBox6.Controls.Add(this.txtData40);
			this.groupBox6.Controls.Add(this.txtData39);
			this.groupBox6.Controls.Add(this.txtData38);
			this.groupBox6.Controls.Add(this.txtData37);
			this.groupBox6.Controls.Add(this.txtData36);
			this.groupBox6.Controls.Add(this.txtData35);
			this.groupBox6.Controls.Add(this.txtData34);
			this.groupBox6.Controls.Add(this.txtData33);
			this.groupBox6.Controls.Add(this.txtData32);
			this.groupBox6.Controls.Add(this.txtData31);
			this.groupBox6.Controls.Add(this.txtData30);
			this.groupBox6.Controls.Add(this.txtData29);
			this.groupBox6.Controls.Add(this.txtData28);
			this.groupBox6.Controls.Add(this.txtData27);
			this.groupBox6.Controls.Add(this.txtData26);
			this.groupBox6.Controls.Add(this.txtData25);
			this.groupBox6.Controls.Add(this.txtData24);
			this.groupBox6.Controls.Add(this.txtData23);
			this.groupBox6.Controls.Add(this.txtData22);
			this.groupBox6.Controls.Add(this.txtData21);
			this.groupBox6.Controls.Add(this.txtData20);
			this.groupBox6.Controls.Add(this.txtData19);
			this.groupBox6.Controls.Add(this.txtData18);
			this.groupBox6.Controls.Add(this.txtData17);
			this.groupBox6.Controls.Add(this.txtData16);
			this.groupBox6.Controls.Add(this.txtData15);
			this.groupBox6.Controls.Add(this.txtData14);
			this.groupBox6.Controls.Add(this.txtData13);
			this.groupBox6.Controls.Add(this.txtData12);
			this.groupBox6.Controls.Add(this.txtData11);
			this.groupBox6.Controls.Add(this.txtData10);
			this.groupBox6.Controls.Add(this.txtData9);
			this.groupBox6.Controls.Add(this.txtData8);
			this.groupBox6.Controls.Add(this.chbBRS);
			this.groupBox6.Controls.Add(this.chbFD);
			this.groupBox6.Controls.Add(this.chbRemote);
			this.groupBox6.Controls.Add(this.chbExtended);
			this.groupBox6.Controls.Add(this.btnWrite);
			this.groupBox6.Controls.Add(this.label12);
			this.groupBox6.Controls.Add(this.label13);
			this.groupBox6.Controls.Add(this.txtID);
			this.groupBox6.Controls.Add(this.txtData7);
			this.groupBox6.Controls.Add(this.txtData6);
			this.groupBox6.Controls.Add(this.txtData5);
			this.groupBox6.Controls.Add(this.txtData4);
			this.groupBox6.Controls.Add(this.txtData3);
			this.groupBox6.Controls.Add(this.txtData2);
			this.groupBox6.Controls.Add(this.txtData1);
			this.groupBox6.Controls.Add(this.txtData0);
			this.groupBox6.Controls.Add(this.nudLength);
			this.groupBox6.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.groupBox6.Location = new System.Drawing.Point(8, 353);
			this.groupBox6.Name = "groupBox6";
			this.groupBox6.Size = new System.Drawing.Size(714, 149);
			this.groupBox6.TabIndex = 51;
			this.groupBox6.TabStop = false;
			this.groupBox6.Text = "Write Messages";
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(665, 16);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(43, 13);
			this.label5.TabIndex = 113;
			this.label5.Text = "Length:";
			// 
			// laLength
			// 
			this.laLength.AutoSize = true;
			this.laLength.Location = new System.Drawing.Point(672, 41);
			this.laLength.Name = "laLength";
			this.laLength.Size = new System.Drawing.Size(26, 13);
			this.laLength.TabIndex = 112;
			this.laLength.Text = "8 B.";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(7, 16);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(61, 13);
			this.label3.TabIndex = 111;
			this.label3.Text = "Data (Hex):";
			// 
			// txtData60
			// 
			this.txtData60.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData60.Enabled = false;
			this.txtData60.Location = new System.Drawing.Point(368, 116);
			this.txtData60.MaxLength = 2;
			this.txtData60.Name = "txtData60";
			this.txtData60.Size = new System.Drawing.Size(24, 20);
			this.txtData60.TabIndex = 66;
			this.txtData60.Text = "00";
			this.txtData60.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData60.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData60.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData62
			// 
			this.txtData62.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData62.Enabled = false;
			this.txtData62.Location = new System.Drawing.Point(428, 116);
			this.txtData62.MaxLength = 2;
			this.txtData62.Name = "txtData62";
			this.txtData62.Size = new System.Drawing.Size(24, 20);
			this.txtData62.TabIndex = 68;
			this.txtData62.Text = "00";
			this.txtData62.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData62.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData62.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData47
			// 
			this.txtData47.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData47.Enabled = false;
			this.txtData47.Location = new System.Drawing.Point(458, 90);
			this.txtData47.MaxLength = 2;
			this.txtData47.Name = "txtData47";
			this.txtData47.Size = new System.Drawing.Size(24, 20);
			this.txtData47.TabIndex = 53;
			this.txtData47.Text = "00";
			this.txtData47.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData47.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData47.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData58
			// 
			this.txtData58.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData58.Enabled = false;
			this.txtData58.Location = new System.Drawing.Point(308, 116);
			this.txtData58.MaxLength = 2;
			this.txtData58.Name = "txtData58";
			this.txtData58.Size = new System.Drawing.Size(24, 20);
			this.txtData58.TabIndex = 64;
			this.txtData58.Text = "00";
			this.txtData58.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData58.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData58.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData57
			// 
			this.txtData57.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData57.Enabled = false;
			this.txtData57.Location = new System.Drawing.Point(278, 116);
			this.txtData57.MaxLength = 2;
			this.txtData57.Name = "txtData57";
			this.txtData57.Size = new System.Drawing.Size(24, 20);
			this.txtData57.TabIndex = 63;
			this.txtData57.Text = "00";
			this.txtData57.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData57.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData57.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData56
			// 
			this.txtData56.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData56.Enabled = false;
			this.txtData56.Location = new System.Drawing.Point(248, 116);
			this.txtData56.MaxLength = 2;
			this.txtData56.Name = "txtData56";
			this.txtData56.Size = new System.Drawing.Size(24, 20);
			this.txtData56.TabIndex = 62;
			this.txtData56.Text = "00";
			this.txtData56.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData56.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData56.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData55
			// 
			this.txtData55.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData55.Enabled = false;
			this.txtData55.Location = new System.Drawing.Point(218, 116);
			this.txtData55.MaxLength = 2;
			this.txtData55.Name = "txtData55";
			this.txtData55.Size = new System.Drawing.Size(24, 20);
			this.txtData55.TabIndex = 61;
			this.txtData55.Text = "00";
			this.txtData55.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData55.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData55.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData54
			// 
			this.txtData54.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData54.Enabled = false;
			this.txtData54.Location = new System.Drawing.Point(188, 116);
			this.txtData54.MaxLength = 2;
			this.txtData54.Name = "txtData54";
			this.txtData54.Size = new System.Drawing.Size(24, 20);
			this.txtData54.TabIndex = 60;
			this.txtData54.Text = "00";
			this.txtData54.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData54.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData54.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData61
			// 
			this.txtData61.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData61.Enabled = false;
			this.txtData61.Location = new System.Drawing.Point(398, 116);
			this.txtData61.MaxLength = 2;
			this.txtData61.Name = "txtData61";
			this.txtData61.Size = new System.Drawing.Size(24, 20);
			this.txtData61.TabIndex = 67;
			this.txtData61.Text = "00";
			this.txtData61.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData61.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData61.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData63
			// 
			this.txtData63.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData63.Enabled = false;
			this.txtData63.Location = new System.Drawing.Point(458, 116);
			this.txtData63.MaxLength = 2;
			this.txtData63.Name = "txtData63";
			this.txtData63.Size = new System.Drawing.Size(24, 20);
			this.txtData63.TabIndex = 69;
			this.txtData63.Text = "00";
			this.txtData63.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData63.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData63.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData48
			// 
			this.txtData48.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData48.Enabled = false;
			this.txtData48.Location = new System.Drawing.Point(8, 116);
			this.txtData48.MaxLength = 2;
			this.txtData48.Name = "txtData48";
			this.txtData48.Size = new System.Drawing.Size(24, 20);
			this.txtData48.TabIndex = 54;
			this.txtData48.Text = "00";
			this.txtData48.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData48.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData48.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData53
			// 
			this.txtData53.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData53.Enabled = false;
			this.txtData53.Location = new System.Drawing.Point(158, 116);
			this.txtData53.MaxLength = 2;
			this.txtData53.Name = "txtData53";
			this.txtData53.Size = new System.Drawing.Size(24, 20);
			this.txtData53.TabIndex = 59;
			this.txtData53.Text = "00";
			this.txtData53.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData53.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData53.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData52
			// 
			this.txtData52.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData52.Enabled = false;
			this.txtData52.Location = new System.Drawing.Point(128, 116);
			this.txtData52.MaxLength = 2;
			this.txtData52.Name = "txtData52";
			this.txtData52.Size = new System.Drawing.Size(24, 20);
			this.txtData52.TabIndex = 58;
			this.txtData52.Text = "00";
			this.txtData52.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData52.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData52.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData51
			// 
			this.txtData51.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData51.Enabled = false;
			this.txtData51.Location = new System.Drawing.Point(98, 116);
			this.txtData51.MaxLength = 2;
			this.txtData51.Name = "txtData51";
			this.txtData51.Size = new System.Drawing.Size(24, 20);
			this.txtData51.TabIndex = 57;
			this.txtData51.Text = "00";
			this.txtData51.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData51.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData51.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData50
			// 
			this.txtData50.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData50.Enabled = false;
			this.txtData50.Location = new System.Drawing.Point(68, 116);
			this.txtData50.MaxLength = 2;
			this.txtData50.Name = "txtData50";
			this.txtData50.Size = new System.Drawing.Size(24, 20);
			this.txtData50.TabIndex = 56;
			this.txtData50.Text = "00";
			this.txtData50.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData50.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData50.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData49
			// 
			this.txtData49.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData49.Enabled = false;
			this.txtData49.Location = new System.Drawing.Point(38, 116);
			this.txtData49.MaxLength = 2;
			this.txtData49.Name = "txtData49";
			this.txtData49.Size = new System.Drawing.Size(24, 20);
			this.txtData49.TabIndex = 55;
			this.txtData49.Text = "00";
			this.txtData49.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData49.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData49.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData59
			// 
			this.txtData59.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData59.Enabled = false;
			this.txtData59.Location = new System.Drawing.Point(338, 116);
			this.txtData59.MaxLength = 2;
			this.txtData59.Name = "txtData59";
			this.txtData59.Size = new System.Drawing.Size(24, 20);
			this.txtData59.TabIndex = 65;
			this.txtData59.Text = "00";
			this.txtData59.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData59.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData59.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData46
			// 
			this.txtData46.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData46.Enabled = false;
			this.txtData46.Location = new System.Drawing.Point(428, 90);
			this.txtData46.MaxLength = 2;
			this.txtData46.Name = "txtData46";
			this.txtData46.Size = new System.Drawing.Size(24, 20);
			this.txtData46.TabIndex = 52;
			this.txtData46.Text = "00";
			this.txtData46.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData46.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData46.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData45
			// 
			this.txtData45.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData45.Enabled = false;
			this.txtData45.Location = new System.Drawing.Point(398, 90);
			this.txtData45.MaxLength = 2;
			this.txtData45.Name = "txtData45";
			this.txtData45.Size = new System.Drawing.Size(24, 20);
			this.txtData45.TabIndex = 51;
			this.txtData45.Text = "00";
			this.txtData45.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData45.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData45.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData44
			// 
			this.txtData44.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData44.Enabled = false;
			this.txtData44.Location = new System.Drawing.Point(368, 90);
			this.txtData44.MaxLength = 2;
			this.txtData44.Name = "txtData44";
			this.txtData44.Size = new System.Drawing.Size(24, 20);
			this.txtData44.TabIndex = 50;
			this.txtData44.Text = "00";
			this.txtData44.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData44.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData44.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData43
			// 
			this.txtData43.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData43.Enabled = false;
			this.txtData43.Location = new System.Drawing.Point(338, 90);
			this.txtData43.MaxLength = 2;
			this.txtData43.Name = "txtData43";
			this.txtData43.Size = new System.Drawing.Size(24, 20);
			this.txtData43.TabIndex = 49;
			this.txtData43.Text = "00";
			this.txtData43.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData43.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData43.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData42
			// 
			this.txtData42.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData42.Enabled = false;
			this.txtData42.Location = new System.Drawing.Point(308, 90);
			this.txtData42.MaxLength = 2;
			this.txtData42.Name = "txtData42";
			this.txtData42.Size = new System.Drawing.Size(24, 20);
			this.txtData42.TabIndex = 48;
			this.txtData42.Text = "00";
			this.txtData42.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData42.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData42.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData41
			// 
			this.txtData41.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData41.Enabled = false;
			this.txtData41.Location = new System.Drawing.Point(278, 90);
			this.txtData41.MaxLength = 2;
			this.txtData41.Name = "txtData41";
			this.txtData41.Size = new System.Drawing.Size(24, 20);
			this.txtData41.TabIndex = 47;
			this.txtData41.Text = "00";
			this.txtData41.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData41.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData41.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData40
			// 
			this.txtData40.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData40.Enabled = false;
			this.txtData40.Location = new System.Drawing.Point(248, 90);
			this.txtData40.MaxLength = 2;
			this.txtData40.Name = "txtData40";
			this.txtData40.Size = new System.Drawing.Size(24, 20);
			this.txtData40.TabIndex = 46;
			this.txtData40.Text = "00";
			this.txtData40.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData40.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData40.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData39
			// 
			this.txtData39.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData39.Enabled = false;
			this.txtData39.Location = new System.Drawing.Point(218, 90);
			this.txtData39.MaxLength = 2;
			this.txtData39.Name = "txtData39";
			this.txtData39.Size = new System.Drawing.Size(24, 20);
			this.txtData39.TabIndex = 45;
			this.txtData39.Text = "00";
			this.txtData39.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData39.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData39.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData38
			// 
			this.txtData38.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData38.Enabled = false;
			this.txtData38.Location = new System.Drawing.Point(188, 90);
			this.txtData38.MaxLength = 2;
			this.txtData38.Name = "txtData38";
			this.txtData38.Size = new System.Drawing.Size(24, 20);
			this.txtData38.TabIndex = 44;
			this.txtData38.Text = "00";
			this.txtData38.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData38.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData38.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData37
			// 
			this.txtData37.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData37.Enabled = false;
			this.txtData37.Location = new System.Drawing.Point(158, 90);
			this.txtData37.MaxLength = 2;
			this.txtData37.Name = "txtData37";
			this.txtData37.Size = new System.Drawing.Size(24, 20);
			this.txtData37.TabIndex = 43;
			this.txtData37.Text = "00";
			this.txtData37.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData37.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData37.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData36
			// 
			this.txtData36.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData36.Enabled = false;
			this.txtData36.Location = new System.Drawing.Point(128, 90);
			this.txtData36.MaxLength = 2;
			this.txtData36.Name = "txtData36";
			this.txtData36.Size = new System.Drawing.Size(24, 20);
			this.txtData36.TabIndex = 42;
			this.txtData36.Text = "00";
			this.txtData36.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData36.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData36.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData35
			// 
			this.txtData35.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData35.Enabled = false;
			this.txtData35.Location = new System.Drawing.Point(98, 90);
			this.txtData35.MaxLength = 2;
			this.txtData35.Name = "txtData35";
			this.txtData35.Size = new System.Drawing.Size(24, 20);
			this.txtData35.TabIndex = 41;
			this.txtData35.Text = "00";
			this.txtData35.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData35.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData35.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData34
			// 
			this.txtData34.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData34.Enabled = false;
			this.txtData34.Location = new System.Drawing.Point(68, 90);
			this.txtData34.MaxLength = 2;
			this.txtData34.Name = "txtData34";
			this.txtData34.Size = new System.Drawing.Size(24, 20);
			this.txtData34.TabIndex = 40;
			this.txtData34.Text = "00";
			this.txtData34.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData34.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData34.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData33
			// 
			this.txtData33.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData33.Enabled = false;
			this.txtData33.Location = new System.Drawing.Point(38, 90);
			this.txtData33.MaxLength = 2;
			this.txtData33.Name = "txtData33";
			this.txtData33.Size = new System.Drawing.Size(24, 20);
			this.txtData33.TabIndex = 39;
			this.txtData33.Text = "00";
			this.txtData33.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData33.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData33.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData32
			// 
			this.txtData32.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData32.Enabled = false;
			this.txtData32.Location = new System.Drawing.Point(8, 90);
			this.txtData32.MaxLength = 2;
			this.txtData32.Name = "txtData32";
			this.txtData32.Size = new System.Drawing.Size(24, 20);
			this.txtData32.TabIndex = 38;
			this.txtData32.Text = "00";
			this.txtData32.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData32.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData32.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData31
			// 
			this.txtData31.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData31.Enabled = false;
			this.txtData31.Location = new System.Drawing.Point(458, 64);
			this.txtData31.MaxLength = 2;
			this.txtData31.Name = "txtData31";
			this.txtData31.Size = new System.Drawing.Size(24, 20);
			this.txtData31.TabIndex = 37;
			this.txtData31.Text = "00";
			this.txtData31.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData31.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData31.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData30
			// 
			this.txtData30.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData30.Enabled = false;
			this.txtData30.Location = new System.Drawing.Point(428, 64);
			this.txtData30.MaxLength = 2;
			this.txtData30.Name = "txtData30";
			this.txtData30.Size = new System.Drawing.Size(24, 20);
			this.txtData30.TabIndex = 36;
			this.txtData30.Text = "00";
			this.txtData30.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData30.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData30.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData29
			// 
			this.txtData29.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData29.Enabled = false;
			this.txtData29.Location = new System.Drawing.Point(398, 64);
			this.txtData29.MaxLength = 2;
			this.txtData29.Name = "txtData29";
			this.txtData29.Size = new System.Drawing.Size(24, 20);
			this.txtData29.TabIndex = 35;
			this.txtData29.Text = "00";
			this.txtData29.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData29.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData29.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData28
			// 
			this.txtData28.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData28.Enabled = false;
			this.txtData28.Location = new System.Drawing.Point(368, 64);
			this.txtData28.MaxLength = 2;
			this.txtData28.Name = "txtData28";
			this.txtData28.Size = new System.Drawing.Size(24, 20);
			this.txtData28.TabIndex = 34;
			this.txtData28.Text = "00";
			this.txtData28.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData28.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData28.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData27
			// 
			this.txtData27.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData27.Enabled = false;
			this.txtData27.Location = new System.Drawing.Point(338, 64);
			this.txtData27.MaxLength = 2;
			this.txtData27.Name = "txtData27";
			this.txtData27.Size = new System.Drawing.Size(24, 20);
			this.txtData27.TabIndex = 33;
			this.txtData27.Text = "00";
			this.txtData27.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData27.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData27.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData26
			// 
			this.txtData26.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData26.Enabled = false;
			this.txtData26.Location = new System.Drawing.Point(308, 64);
			this.txtData26.MaxLength = 2;
			this.txtData26.Name = "txtData26";
			this.txtData26.Size = new System.Drawing.Size(24, 20);
			this.txtData26.TabIndex = 32;
			this.txtData26.Text = "00";
			this.txtData26.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData26.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData26.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData25
			// 
			this.txtData25.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData25.Enabled = false;
			this.txtData25.Location = new System.Drawing.Point(278, 64);
			this.txtData25.MaxLength = 2;
			this.txtData25.Name = "txtData25";
			this.txtData25.Size = new System.Drawing.Size(24, 20);
			this.txtData25.TabIndex = 31;
			this.txtData25.Text = "00";
			this.txtData25.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData25.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData25.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData24
			// 
			this.txtData24.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData24.Enabled = false;
			this.txtData24.Location = new System.Drawing.Point(248, 64);
			this.txtData24.MaxLength = 2;
			this.txtData24.Name = "txtData24";
			this.txtData24.Size = new System.Drawing.Size(24, 20);
			this.txtData24.TabIndex = 30;
			this.txtData24.Text = "00";
			this.txtData24.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData24.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData24.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData23
			// 
			this.txtData23.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData23.Enabled = false;
			this.txtData23.Location = new System.Drawing.Point(218, 64);
			this.txtData23.MaxLength = 2;
			this.txtData23.Name = "txtData23";
			this.txtData23.Size = new System.Drawing.Size(24, 20);
			this.txtData23.TabIndex = 29;
			this.txtData23.Text = "00";
			this.txtData23.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData23.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData23.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData22
			// 
			this.txtData22.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData22.Enabled = false;
			this.txtData22.Location = new System.Drawing.Point(188, 64);
			this.txtData22.MaxLength = 2;
			this.txtData22.Name = "txtData22";
			this.txtData22.Size = new System.Drawing.Size(24, 20);
			this.txtData22.TabIndex = 28;
			this.txtData22.Text = "00";
			this.txtData22.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData22.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData22.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData21
			// 
			this.txtData21.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData21.Enabled = false;
			this.txtData21.Location = new System.Drawing.Point(158, 64);
			this.txtData21.MaxLength = 2;
			this.txtData21.Name = "txtData21";
			this.txtData21.Size = new System.Drawing.Size(24, 20);
			this.txtData21.TabIndex = 27;
			this.txtData21.Text = "00";
			this.txtData21.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData21.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData21.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData20
			// 
			this.txtData20.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData20.Enabled = false;
			this.txtData20.Location = new System.Drawing.Point(128, 64);
			this.txtData20.MaxLength = 2;
			this.txtData20.Name = "txtData20";
			this.txtData20.Size = new System.Drawing.Size(24, 20);
			this.txtData20.TabIndex = 26;
			this.txtData20.Text = "00";
			this.txtData20.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData20.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData20.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData19
			// 
			this.txtData19.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData19.Enabled = false;
			this.txtData19.Location = new System.Drawing.Point(98, 64);
			this.txtData19.MaxLength = 2;
			this.txtData19.Name = "txtData19";
			this.txtData19.Size = new System.Drawing.Size(24, 20);
			this.txtData19.TabIndex = 25;
			this.txtData19.Text = "00";
			this.txtData19.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData19.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData19.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData18
			// 
			this.txtData18.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData18.Enabled = false;
			this.txtData18.Location = new System.Drawing.Point(69, 64);
			this.txtData18.MaxLength = 2;
			this.txtData18.Name = "txtData18";
			this.txtData18.Size = new System.Drawing.Size(24, 20);
			this.txtData18.TabIndex = 24;
			this.txtData18.Text = "00";
			this.txtData18.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData18.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData18.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData17
			// 
			this.txtData17.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData17.Enabled = false;
			this.txtData17.Location = new System.Drawing.Point(39, 64);
			this.txtData17.MaxLength = 2;
			this.txtData17.Name = "txtData17";
			this.txtData17.Size = new System.Drawing.Size(24, 20);
			this.txtData17.TabIndex = 23;
			this.txtData17.Text = "00";
			this.txtData17.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData17.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData17.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData16
			// 
			this.txtData16.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData16.Enabled = false;
			this.txtData16.Location = new System.Drawing.Point(8, 64);
			this.txtData16.MaxLength = 2;
			this.txtData16.Name = "txtData16";
			this.txtData16.Size = new System.Drawing.Size(24, 20);
			this.txtData16.TabIndex = 22;
			this.txtData16.Text = "00";
			this.txtData16.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData16.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData16.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData15
			// 
			this.txtData15.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData15.Enabled = false;
			this.txtData15.Location = new System.Drawing.Point(458, 38);
			this.txtData15.MaxLength = 2;
			this.txtData15.Name = "txtData15";
			this.txtData15.Size = new System.Drawing.Size(24, 20);
			this.txtData15.TabIndex = 21;
			this.txtData15.Text = "00";
			this.txtData15.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData15.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData15.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData14
			// 
			this.txtData14.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData14.Enabled = false;
			this.txtData14.Location = new System.Drawing.Point(428, 38);
			this.txtData14.MaxLength = 2;
			this.txtData14.Name = "txtData14";
			this.txtData14.Size = new System.Drawing.Size(24, 20);
			this.txtData14.TabIndex = 20;
			this.txtData14.Text = "00";
			this.txtData14.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData14.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData14.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData13
			// 
			this.txtData13.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData13.Enabled = false;
			this.txtData13.Location = new System.Drawing.Point(398, 38);
			this.txtData13.MaxLength = 2;
			this.txtData13.Name = "txtData13";
			this.txtData13.Size = new System.Drawing.Size(24, 20);
			this.txtData13.TabIndex = 19;
			this.txtData13.Text = "00";
			this.txtData13.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData13.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData13.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData12
			// 
			this.txtData12.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData12.Enabled = false;
			this.txtData12.Location = new System.Drawing.Point(368, 38);
			this.txtData12.MaxLength = 2;
			this.txtData12.Name = "txtData12";
			this.txtData12.Size = new System.Drawing.Size(24, 20);
			this.txtData12.TabIndex = 18;
			this.txtData12.Text = "00";
			this.txtData12.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData12.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData12.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData11
			// 
			this.txtData11.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData11.Enabled = false;
			this.txtData11.Location = new System.Drawing.Point(338, 38);
			this.txtData11.MaxLength = 2;
			this.txtData11.Name = "txtData11";
			this.txtData11.Size = new System.Drawing.Size(24, 20);
			this.txtData11.TabIndex = 17;
			this.txtData11.Text = "00";
			this.txtData11.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData11.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData11.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData10
			// 
			this.txtData10.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData10.Enabled = false;
			this.txtData10.Location = new System.Drawing.Point(308, 38);
			this.txtData10.MaxLength = 2;
			this.txtData10.Name = "txtData10";
			this.txtData10.Size = new System.Drawing.Size(24, 20);
			this.txtData10.TabIndex = 16;
			this.txtData10.Text = "00";
			this.txtData10.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData10.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData10.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData9
			// 
			this.txtData9.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData9.Enabled = false;
			this.txtData9.Location = new System.Drawing.Point(278, 38);
			this.txtData9.MaxLength = 2;
			this.txtData9.Name = "txtData9";
			this.txtData9.Size = new System.Drawing.Size(24, 20);
			this.txtData9.TabIndex = 15;
			this.txtData9.Text = "00";
			this.txtData9.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData9.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData9.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData8
			// 
			this.txtData8.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData8.Enabled = false;
			this.txtData8.Location = new System.Drawing.Point(248, 38);
			this.txtData8.MaxLength = 2;
			this.txtData8.Name = "txtData8";
			this.txtData8.Size = new System.Drawing.Size(24, 20);
			this.txtData8.TabIndex = 14;
			this.txtData8.Text = "00";
			this.txtData8.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData8.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData8.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// chbBRS
			// 
			this.chbBRS.Cursor = System.Windows.Forms.Cursors.Default;
			this.chbBRS.Enabled = false;
			this.chbBRS.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.chbBRS.Location = new System.Drawing.Point(568, 86);
			this.chbBRS.Name = "chbBRS";
			this.chbBRS.Size = new System.Drawing.Size(40, 24);
			this.chbBRS.TabIndex = 5;
			this.chbBRS.Text = "BRS";
			this.chbBRS.Visible = false;
			// 
			// chbFD
			// 
			this.chbFD.Cursor = System.Windows.Forms.Cursors.Default;
			this.chbFD.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.chbFD.Location = new System.Drawing.Point(500, 86);
			this.chbFD.Name = "chbFD";
			this.chbFD.Size = new System.Drawing.Size(37, 24);
			this.chbFD.TabIndex = 4;
			this.chbFD.Text = "FD";
			this.chbFD.Visible = false;
			this.chbFD.CheckedChanged += new System.EventHandler(this.chbFD_CheckedChanged);
			// 
			// chbRemote
			// 
			this.chbRemote.Cursor = System.Windows.Forms.Cursors.Default;
			this.chbRemote.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.chbRemote.Location = new System.Drawing.Point(568, 64);
			this.chbRemote.Name = "chbRemote";
			this.chbRemote.Size = new System.Drawing.Size(44, 24);
			this.chbRemote.TabIndex = 3;
			this.chbRemote.Text = "RTR";
			this.chbRemote.CheckedChanged += new System.EventHandler(this.chbRemote_CheckedChanged);
			// 
			// chbExtended
			// 
			this.chbExtended.Cursor = System.Windows.Forms.Cursors.Default;
			this.chbExtended.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.chbExtended.Location = new System.Drawing.Point(500, 64);
			this.chbExtended.Name = "chbExtended";
			this.chbExtended.Size = new System.Drawing.Size(62, 24);
			this.chbExtended.TabIndex = 2;
			this.chbExtended.Text = "Extended";
			this.chbExtended.CheckedChanged += new System.EventHandler(this.chbExtended_CheckedChanged);
			// 
			// btnWrite
			// 
			this.btnWrite.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btnWrite.Cursor = System.Windows.Forms.Cursors.Default;
			this.btnWrite.Enabled = false;
			this.btnWrite.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.btnWrite.Location = new System.Drawing.Point(639, 86);
			this.btnWrite.Name = "btnWrite";
			this.btnWrite.Size = new System.Drawing.Size(65, 23);
			this.btnWrite.TabIndex = 6;
			this.btnWrite.Text = "Write";
			this.btnWrite.Click += new System.EventHandler(this.btnWrite_Click);
			// 
			// label12
			// 
			this.label12.AutoSize = true;
			this.label12.Location = new System.Drawing.Point(615, 16);
			this.label12.Name = "label12";
			this.label12.Size = new System.Drawing.Size(31, 13);
			this.label12.TabIndex = 31;
			this.label12.Text = "DLC:";
			// 
			// label13
			// 
			this.label13.AutoSize = true;
			this.label13.Location = new System.Drawing.Point(497, 16);
			this.label13.Name = "label13";
			this.label13.Size = new System.Drawing.Size(49, 13);
			this.label13.TabIndex = 30;
			this.label13.Text = "ID (Hex):";
			// 
			// txtID
			// 
			this.txtID.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtID.Location = new System.Drawing.Point(500, 38);
			this.txtID.MaxLength = 3;
			this.txtID.Name = "txtID";
			this.txtID.Size = new System.Drawing.Size(112, 20);
			this.txtID.TabIndex = 0;
			this.txtID.Text = "0";
			this.txtID.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtID.Leave += new System.EventHandler(this.txtID_Leave);
			// 
			// txtData7
			// 
			this.txtData7.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData7.Location = new System.Drawing.Point(218, 38);
			this.txtData7.MaxLength = 2;
			this.txtData7.Name = "txtData7";
			this.txtData7.Size = new System.Drawing.Size(24, 20);
			this.txtData7.TabIndex = 13;
			this.txtData7.Text = "00";
			this.txtData7.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData7.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData7.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData6
			// 
			this.txtData6.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData6.Location = new System.Drawing.Point(188, 38);
			this.txtData6.MaxLength = 2;
			this.txtData6.Name = "txtData6";
			this.txtData6.Size = new System.Drawing.Size(24, 20);
			this.txtData6.TabIndex = 12;
			this.txtData6.Text = "00";
			this.txtData6.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData6.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData6.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData5
			// 
			this.txtData5.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData5.Location = new System.Drawing.Point(158, 38);
			this.txtData5.MaxLength = 2;
			this.txtData5.Name = "txtData5";
			this.txtData5.Size = new System.Drawing.Size(24, 20);
			this.txtData5.TabIndex = 11;
			this.txtData5.Text = "00";
			this.txtData5.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData5.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData5.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData4
			// 
			this.txtData4.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData4.Location = new System.Drawing.Point(128, 38);
			this.txtData4.MaxLength = 2;
			this.txtData4.Name = "txtData4";
			this.txtData4.Size = new System.Drawing.Size(24, 20);
			this.txtData4.TabIndex = 10;
			this.txtData4.Text = "00";
			this.txtData4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData4.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData4.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData3
			// 
			this.txtData3.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData3.Location = new System.Drawing.Point(98, 38);
			this.txtData3.MaxLength = 2;
			this.txtData3.Name = "txtData3";
			this.txtData3.Size = new System.Drawing.Size(24, 20);
			this.txtData3.TabIndex = 9;
			this.txtData3.Text = "00";
			this.txtData3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData3.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData3.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData2
			// 
			this.txtData2.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData2.Location = new System.Drawing.Point(69, 38);
			this.txtData2.MaxLength = 2;
			this.txtData2.Name = "txtData2";
			this.txtData2.Size = new System.Drawing.Size(24, 20);
			this.txtData2.TabIndex = 8;
			this.txtData2.Text = "00";
			this.txtData2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData2.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData2.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData1
			// 
			this.txtData1.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData1.Location = new System.Drawing.Point(39, 38);
			this.txtData1.MaxLength = 2;
			this.txtData1.Name = "txtData1";
			this.txtData1.Size = new System.Drawing.Size(24, 20);
			this.txtData1.TabIndex = 7;
			this.txtData1.Text = "00";
			this.txtData1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData1.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData1.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// txtData0
			// 
			this.txtData0.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
			this.txtData0.Location = new System.Drawing.Point(8, 38);
			this.txtData0.MaxLength = 2;
			this.txtData0.Name = "txtData0";
			this.txtData0.Size = new System.Drawing.Size(24, 20);
			this.txtData0.TabIndex = 6;
			this.txtData0.Text = "00";
			this.txtData0.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.txtData0.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtID_KeyPress);
			this.txtData0.Leave += new System.EventHandler(this.txtData0_Leave);
			// 
			// nudLength
			// 
			this.nudLength.BackColor = System.Drawing.Color.White;
			this.nudLength.Location = new System.Drawing.Point(618, 38);
			this.nudLength.Maximum = new decimal(new int[] {
            8,
            0,
            0,
            0});
			this.nudLength.Name = "nudLength";
			this.nudLength.ReadOnly = true;
			this.nudLength.Size = new System.Drawing.Size(41, 20);
			this.nudLength.TabIndex = 1;
			this.nudLength.Value = new decimal(new int[] {
            8,
            0,
            0,
            0});
			this.nudLength.ValueChanged += new System.EventHandler(this.nudLength_ValueChanged);
			// 
			// tmrRead
			// 
			this.tmrRead.Interval = 50;
			this.tmrRead.Tick += new System.EventHandler(this.tmrRead_Tick);
			// 
			// tmrDisplay
			// 
			this.tmrDisplay.Tick += new System.EventHandler(this.tmrDisplay_Tick);
			// 
			// Form1
			// 
			this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			this.ClientSize = new System.Drawing.Size(734, 605);
			this.Controls.Add(this.groupBox6);
			this.Controls.Add(this.groupBox5);
			this.Controls.Add(this.groupBox4);
			this.Controls.Add(this.groupBox2);
			this.Controls.Add(this.groupBox3);
			this.Controls.Add(this.groupBox1);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
			this.MaximizeBox = false;
			this.Name = "Form1";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "PCAN-Basic Sample";
			this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Form1_FormClosing);
			this.groupBox1.ResumeLayout(false);
			this.groupBox1.PerformLayout();
			this.groupBox3.ResumeLayout(false);
			this.groupBox3.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nudIdTo)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nudIdFrom)).EndInit();
			this.groupBox2.ResumeLayout(false);
			((System.ComponentModel.ISupportInitialize)(this.nudDelay)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nudDeviceId)).EndInit();
			this.groupBox4.ResumeLayout(false);
			this.groupBox5.ResumeLayout(false);
			this.groupBox5.PerformLayout();
			this.groupBox6.ResumeLayout(false);
			this.groupBox6.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nudLength)).EndInit();
			this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox cbbChannel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnRelease;
        private System.Windows.Forms.Button btnInit;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.NumericUpDown nudIdFrom;
        private System.Windows.Forms.RadioButton rdbFilterOpen;
        private System.Windows.Forms.RadioButton rdbFilterCustom;
        private System.Windows.Forms.RadioButton rdbFilterClose;
        private System.Windows.Forms.Button btnFilterApply;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown nudIdTo;
        private System.Windows.Forms.CheckBox chbFilterExt;
        private System.Windows.Forms.Button btnParameterSet;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.RadioButton rdbParamActive;
        private System.Windows.Forms.RadioButton rdbParamInactive;
        private System.Windows.Forms.ComboBox cbbParameter;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown nudDeviceId;
        private System.Windows.Forms.Label laDeviceOrDelay;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.RadioButton rdbEvent;
        private System.Windows.Forms.ListView lstMessages;
        private System.Windows.Forms.ColumnHeader clhType;
        private System.Windows.Forms.ColumnHeader clhID;
        private System.Windows.Forms.ColumnHeader clhLength;
        private System.Windows.Forms.ColumnHeader clhData;
        private System.Windows.Forms.ColumnHeader clhCount;
        private System.Windows.Forms.ColumnHeader clhRcvTime;
        private System.Windows.Forms.RadioButton rdbTimer;
        private System.Windows.Forms.RadioButton rdbManual;
        private System.Windows.Forms.Button btnRead;
        private System.Windows.Forms.Button btnMsgClear;
        private System.Windows.Forms.CheckBox chbShowPeriod;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.CheckBox chbRemote;
        private System.Windows.Forms.CheckBox chbExtended;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox txtID;
        private System.Windows.Forms.TextBox txtData7;
        private System.Windows.Forms.TextBox txtData6;
        private System.Windows.Forms.TextBox txtData5;
        private System.Windows.Forms.TextBox txtData4;
        private System.Windows.Forms.TextBox txtData3;
        private System.Windows.Forms.TextBox txtData2;
        private System.Windows.Forms.TextBox txtData1;
        private System.Windows.Forms.TextBox txtData0;
        private System.Windows.Forms.NumericUpDown nudLength;
        private System.Windows.Forms.Button btnWrite;
        private System.Windows.Forms.Button btnInfoClear;
        private System.Windows.Forms.Button btnGetVersions;
        private System.Windows.Forms.Button btnParameterGet;
        private System.Windows.Forms.Button btnFilterQuery;
        private System.Windows.Forms.ListBox lbxInfo;
        private System.Windows.Forms.Timer tmrRead;
        private System.Windows.Forms.Button btnHwRefresh;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Button btnStatus;
        private System.Windows.Forms.Timer tmrDisplay;
        private System.Windows.Forms.Label laBitrate;
        private System.Windows.Forms.TextBox txtBitrate;
        private System.Windows.Forms.CheckBox chbFD;
        private System.Windows.Forms.TextBox txtData15;
        private System.Windows.Forms.TextBox txtData14;
        private System.Windows.Forms.TextBox txtData13;
        private System.Windows.Forms.TextBox txtData12;
        private System.Windows.Forms.TextBox txtData11;
        private System.Windows.Forms.TextBox txtData10;
        private System.Windows.Forms.TextBox txtData9;
        private System.Windows.Forms.TextBox txtData8;
        private System.Windows.Forms.CheckBox chbBRS;
        private System.Windows.Forms.TextBox txtData60;
        private System.Windows.Forms.TextBox txtData62;
        private System.Windows.Forms.TextBox txtData47;
        private System.Windows.Forms.TextBox txtData58;
        private System.Windows.Forms.TextBox txtData57;
        private System.Windows.Forms.TextBox txtData56;
        private System.Windows.Forms.TextBox txtData55;
        private System.Windows.Forms.TextBox txtData54;
        private System.Windows.Forms.TextBox txtData61;
        private System.Windows.Forms.TextBox txtData63;
        private System.Windows.Forms.TextBox txtData48;
        private System.Windows.Forms.TextBox txtData53;
        private System.Windows.Forms.TextBox txtData52;
        private System.Windows.Forms.TextBox txtData51;
        private System.Windows.Forms.TextBox txtData50;
        private System.Windows.Forms.TextBox txtData49;
        private System.Windows.Forms.TextBox txtData59;
        private System.Windows.Forms.TextBox txtData46;
        private System.Windows.Forms.TextBox txtData45;
        private System.Windows.Forms.TextBox txtData44;
        private System.Windows.Forms.TextBox txtData43;
        private System.Windows.Forms.TextBox txtData42;
        private System.Windows.Forms.TextBox txtData41;
        private System.Windows.Forms.TextBox txtData40;
        private System.Windows.Forms.TextBox txtData39;
        private System.Windows.Forms.TextBox txtData38;
        private System.Windows.Forms.TextBox txtData37;
        private System.Windows.Forms.TextBox txtData36;
        private System.Windows.Forms.TextBox txtData35;
        private System.Windows.Forms.TextBox txtData34;
        private System.Windows.Forms.TextBox txtData33;
        private System.Windows.Forms.TextBox txtData32;
        private System.Windows.Forms.TextBox txtData31;
        private System.Windows.Forms.TextBox txtData30;
        private System.Windows.Forms.TextBox txtData29;
        private System.Windows.Forms.TextBox txtData28;
        private System.Windows.Forms.TextBox txtData27;
        private System.Windows.Forms.TextBox txtData26;
        private System.Windows.Forms.TextBox txtData25;
        private System.Windows.Forms.TextBox txtData24;
        private System.Windows.Forms.TextBox txtData23;
        private System.Windows.Forms.TextBox txtData22;
        private System.Windows.Forms.TextBox txtData21;
        private System.Windows.Forms.TextBox txtData20;
        private System.Windows.Forms.TextBox txtData19;
        private System.Windows.Forms.TextBox txtData18;
        private System.Windows.Forms.TextBox txtData17;
        private System.Windows.Forms.TextBox txtData16;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label laLength;
        private System.Windows.Forms.ComboBox cbbHwType;
        private System.Windows.Forms.ComboBox cbbInterrupt;
        private System.Windows.Forms.Label laInterrupt;
        private System.Windows.Forms.ComboBox cbbIO;
        private System.Windows.Forms.Label laIOPort;
        private System.Windows.Forms.Label laHwType;
        private System.Windows.Forms.ComboBox cbbBaudrates;
        private System.Windows.Forms.Label laBaudrate;
        private System.Windows.Forms.CheckBox chbCanFD;
        private System.Windows.Forms.NumericUpDown nudDelay;
    }
}

